/* product-list.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background: white;
  border-bottom: 1rpx solid #eee;
  padding: 0 20rpx;
}

.filter-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.filter-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 40rpx;
  background-color: #eee;
}

.filter-text {
  font-size: 26rpx;
  color: #333;
}

.filter-arrow {
  font-size: 20rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 商品列表 */
.product-list {
  flex: 1;
}

.product-container {
  padding: 20rpx;
}

/* 网格布局 */
.product-container.grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.product-container.grid .product-item {
  width: calc(50% - 10rpx);
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.product-container.grid .product-image {
  width: 100%;
  height: 300rpx;
}

.product-container.grid .product-info {
  padding: 20rpx;
}

/* 列表布局 */
.product-container.list .product-item {
  display: flex;
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.product-container.list .product-image {
  width: 200rpx;
  height: 200rpx;
  flex-shrink: 0;
}

.product-container.list .product-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 商品信息 */
.product-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 1.4;
}

.product-price {
  margin-bottom: 10rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #FF6B9D;
}

.price-value {
  font-size: 28rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 10rpx;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 10rpx;
}

.tag {
  padding: 4rpx 8rpx;
  background-color: #FF6B9D;
  color: white;
  font-size: 20rpx;
  border-radius: 8rpx;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

.rating {
  color: #ff9500;
}

/* 加载状态 */
.load-status {
  padding: 40rpx;
  text-align: center;
  font-size: 26rpx;
  color: #999;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.modal-content {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 排序选项 */
.sort-options {
  padding: 20rpx 0;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  font-size: 28rpx;
}

.sort-option.active {
  color: #FF6B9D;
}

.check {
  color: #FF6B9D;
  font-weight: bold;
}

/* 筛选弹窗 */
.filter-modal {
  align-items: center;
  max-height: 90vh;
}

.filter-content {
  padding: 20rpx 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.price-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.price-separator {
  color: #999;
}

.tag-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.tag-option {
  padding: 15rpx 25rpx;
  border: 1rpx solid #ddd;
  border-radius: 25rpx;
  font-size: 24rpx;
  color: #666;
}

.tag-option.active {
  background-color: #FF6B9D;
  border-color: #FF6B9D;
  color: white;
}

.filter-actions {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #eee;
}

.filter-reset,
.filter-confirm {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.filter-reset {
  background-color: #f5f5f5;
  color: #666;
}

.filter-confirm {
  background-color: #FF6B9D;
  color: white;
}
