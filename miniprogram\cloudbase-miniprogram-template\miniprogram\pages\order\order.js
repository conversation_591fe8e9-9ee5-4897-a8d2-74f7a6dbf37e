// order.js
Page({
  data: {
    orderData: {},
    selectedAddress: null,
    addressList: [],
    showAddressModal: false,
    
    // 配送选项
    deliveryOptions: [
      { id: 1, name: '标准快递', desc: '3-5个工作日', price: 0 },
      { id: 2, name: '顺丰快递', desc: '1-2个工作日', price: 15 },
      { id: 3, name: '京东快递', desc: '次日达', price: 20 }
    ],
    selectedDelivery: 1,
    deliveryFee: 0,
    
    // 支付选项
    paymentOptions: [
      { id: 1, name: '微信支付', icon: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=50&h=50&fit=crop' },
      { id: 2, name: '支付宝', icon: 'https://images.unsplash.com/photo-1611162616305-c69b3fa7fbe0?w=50&h=50&fit=crop' }
    ],
    selectedPayment: 1,
    
    // 优惠券
    selectedCoupon: null,
    couponDiscount: 0,
    
    // 订单备注
    remark: '',
    
    // 计算结果
    finalAmount: 0,
    canSubmit: false
  },

  onLoad(options) {
    if (options.data) {
      try {
        const orderData = JSON.parse(decodeURIComponent(options.data))
        this.setData({ orderData })
        this.calculateAmount()
      } catch (error) {
        console.error('解析订单数据失败：', error)
        wx.showToast({
          title: '数据错误',
          icon: 'none'
        })
      }
    }
    
    this.loadAddressList()
  },

  // 加载地址列表
  async loadAddressList() {
    try {
      // 模拟数据，实际应该从云数据库获取
      const mockAddressList = [
        {
          id: 1,
          name: '张三',
          phone: '138****8888',
          province: '广东省',
          city: '深圳市',
          district: '南山区',
          detail: '科技园南区深南大道10000号',
          isDefault: true
        },
        {
          id: 2,
          name: '李四',
          phone: '139****9999',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '建国门外大街1号',
          isDefault: false
        }
      ]

      const defaultAddress = mockAddressList.find(addr => addr.isDefault) || mockAddressList[0]
      
      this.setData({ 
        addressList: mockAddressList,
        selectedAddress: defaultAddress
      })
      
      this.checkCanSubmit()
    } catch (error) {
      console.error('加载地址失败：', error)
    }
  },

  // 计算总金额
  calculateAmount() {
    const { orderData, deliveryFee, couponDiscount } = this.data
    const productTotal = parseFloat(orderData.totalAmount || 0)
    const finalAmount = Math.max(0, productTotal + deliveryFee - couponDiscount)
    
    this.setData({ finalAmount: finalAmount.toFixed(2) })
    this.checkCanSubmit()
  },

  // 检查是否可以提交订单
  checkCanSubmit() {
    const { selectedAddress, selectedPayment, orderData } = this.data
    const canSubmit = selectedAddress && selectedPayment && orderData.products && orderData.products.length > 0
    this.setData({ canSubmit })
  },

  // 选择收货地址
  selectAddress() {
    this.setData({ showAddressModal: true })
  },

  // 隐藏地址选择弹窗
  hideAddressModal() {
    this.setData({ showAddressModal: false })
  },

  // 选择地址
  chooseAddress(e) {
    const index = e.currentTarget.dataset.index
    const selectedAddress = this.data.addressList[index]
    
    this.setData({ 
      selectedAddress,
      showAddressModal: false
    })
    
    this.checkCanSubmit()
  },

  // 新增地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  },

  // 选择配送方式
  selectDelivery(e) {
    const deliveryId = parseInt(e.currentTarget.dataset.id)
    const delivery = this.data.deliveryOptions.find(item => item.id === deliveryId)
    
    this.setData({ 
      selectedDelivery: deliveryId,
      deliveryFee: delivery ? delivery.price : 0
    })
    
    this.calculateAmount()
  },

  // 选择支付方式
  selectPayment(e) {
    const paymentId = parseInt(e.currentTarget.dataset.id)
    this.setData({ selectedPayment: paymentId })
    this.checkCanSubmit()
  },

  // 选择优惠券
  selectCoupon() {
    // 跳转到优惠券选择页面
    wx.navigateTo({
      url: '/pages/coupon-list/coupon-list'
    })
  },

  // 订单备注输入
  onRemarkInput(e) {
    this.setData({ remark: e.detail.value })
  },

  // 提交订单
  async submitOrder() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善订单信息',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '提交中...'
    })

    try {
      // 构造订单数据
      const orderInfo = {
        products: this.data.orderData.products,
        address: this.data.selectedAddress,
        delivery: this.data.deliveryOptions.find(item => item.id === this.data.selectedDelivery),
        payment: this.data.paymentOptions.find(item => item.id === this.data.selectedPayment),
        coupon: this.data.selectedCoupon,
        remark: this.data.remark,
        amounts: {
          productTotal: parseFloat(this.data.orderData.totalAmount),
          deliveryFee: this.data.deliveryFee,
          couponDiscount: this.data.couponDiscount,
          finalAmount: parseFloat(this.data.finalAmount)
        }
      }

      // 这里应该调用云函数创建订单
      // const result = await wx.cloud.callFunction({
      //   name: 'createOrder',
      //   data: orderInfo
      // })

      // 模拟创建订单成功
      const mockOrderId = 'ORDER' + Date.now()
      
      wx.hideLoading()
      
      // 跳转到支付页面或订单详情页面
      if (this.data.selectedPayment === 1) {
        // 微信支付
        this.wxPay(mockOrderId)
      } else {
        // 其他支付方式，跳转到订单详情
        wx.redirectTo({
          url: `/pages/order-detail/order-detail?id=${mockOrderId}`
        })
      }
      
    } catch (error) {
      wx.hideLoading()
      console.error('提交订单失败：', error)
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      })
    }
  },

  // 微信支付
  async wxPay(orderId) {
    try {
      // 这里应该调用云函数获取支付参数
      // const payResult = await wx.cloud.callFunction({
      //   name: 'createPayment',
      //   data: { orderId }
      // })

      // 模拟支付参数
      const paymentParams = {
        timeStamp: String(Date.now()),
        nonceStr: 'random_string',
        package: 'prepay_id=mock_prepay_id',
        signType: 'RSA',
        paySign: 'mock_pay_sign'
      }

      // 调起微信支付
      wx.requestPayment({
        ...paymentParams,
        success: (res) => {
          wx.showToast({
            title: '支付成功',
            icon: 'success'
          })
          
          // 跳转到订单详情页面
          setTimeout(() => {
            wx.redirectTo({
              url: `/pages/order-detail/order-detail?id=${orderId}`
            })
          }, 1500)
        },
        fail: (err) => {
          console.error('支付失败：', err)
          if (err.errMsg !== 'requestPayment:fail cancel') {
            wx.showToast({
              title: '支付失败',
              icon: 'none'
            })
          }
          
          // 跳转到订单详情页面
          wx.redirectTo({
            url: `/pages/order-detail/order-detail?id=${orderId}`
          })
        }
      })
    } catch (error) {
      console.error('发起支付失败：', error)
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      })
    }
  }
})
