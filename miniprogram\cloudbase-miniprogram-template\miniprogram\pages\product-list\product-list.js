// product-list.js
Page({
  data: {
    products: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    
    // 筛选和排序
    layoutType: 'grid', // grid 或 list
    currentSort: 'default',
    sortText: '综合排序',
    showSort: false,
    showFilter: false,
    
    // 排序选项
    sortOptions: [
      { label: '综合排序', value: 'default' },
      { label: '价格从低到高', value: 'price_asc' },
      { label: '价格从高到低', value: 'price_desc' },
      { label: '销量优先', value: 'sales' },
      { label: '最新上架', value: 'newest' }
    ],
    
    // 筛选条件
    minPrice: '',
    maxPrice: '',
    selectedTags: [],
    tagOptions: ['热销', '新品', '限定', '现货', '预售', '包邮'],
    
    // 页面参数
    category: '',
    type: '',
    keyword: ''
  },

  onLoad(options) {
    // 获取页面参数
    if (options.category) {
      this.setData({ category: options.category })
      wx.setNavigationBarTitle({
        title: this.getCategoryName(options.category)
      })
    }
    if (options.type) {
      this.setData({ type: options.type })
      wx.setNavigationBarTitle({
        title: options.type === 'hot' ? '热销商品' : '新品上架'
      })
    }
    if (options.keyword) {
      this.setData({ keyword: options.keyword })
      wx.setNavigationBarTitle({
        title: `搜索: ${options.keyword}`
      })
    }
    
    this.loadProducts(true)
  },

  onPullDownRefresh() {
    this.loadProducts(true)
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    this.loadMore()
  },

  // 获取分类名称
  getCategoryName(category) {
    const categoryMap = {
      'anime': '动漫手办',
      'game': '游戏手办',
      'movie': '影视手办',
      'limited': '限定版'
    }
    return categoryMap[category] || '商品列表'
  },

  // 加载商品数据
  async loadProducts(reset = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      // 模拟数据，实际应该从云数据库获取
      const mockProducts = [
        {
          id: 1,
          name: '初音未来 雪初音版 1/8 完成品手办',
          price: 299.00,
          originalPrice: 399.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
          category: 'anime',
          tags: ['热销', '现货'],
          sales: 1234,
          rating: 4.8
        },
        {
          id: 2,
          name: '海贼王 路飞 四档 战斗版手办',
          price: 399.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=400&h=400&fit=crop',
          category: 'anime',
          tags: ['新品', '预售'],
          sales: 567,
          rating: 4.9
        },
        {
          id: 3,
          name: '鬼灭之刃 炭治郎 DX版手办',
          price: 259.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
          category: 'anime',
          tags: ['限定', '包邮'],
          sales: 890,
          rating: 4.7
        },
        {
          id: 4,
          name: '原神 甘雨 1/7 完成品手办',
          price: 459.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=400&h=400&fit=crop',
          category: 'game',
          tags: ['热销', '现货'],
          sales: 2345,
          rating: 4.9
        },
        {
          id: 5,
          name: '明日方舟 德克萨斯 手办',
          price: 329.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
          category: 'game',
          tags: ['现货', '包邮'],
          sales: 456,
          rating: 4.6
        },
        {
          id: 6,
          name: '高达 RX-78-2 Ver.Ka 模型',
          price: 599.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=400&h=400&fit=crop',
          category: 'gundam',
          tags: ['经典', '现货'],
          sales: 123,
          rating: 5.0
        }
      ]

      // 应用筛选条件
      let filteredProducts = this.applyFilters(mockProducts)
      
      // 应用排序
      filteredProducts = this.applySorting(filteredProducts)

      if (reset) {
        this.setData({
          products: filteredProducts,
          page: 1,
          hasMore: filteredProducts.length >= this.data.pageSize
        })
      } else {
        this.setData({
          products: [...this.data.products, ...filteredProducts],
          page: this.data.page + 1,
          hasMore: filteredProducts.length >= this.data.pageSize
        })
      }
    } catch (error) {
      console.error('加载商品失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 应用筛选条件
  applyFilters(products) {
    return products.filter(product => {
      // 分类筛选
      if (this.data.category && product.category !== this.data.category) {
        return false
      }
      
      // 类型筛选
      if (this.data.type === 'hot' && product.sales < 500) {
        return false
      }
      if (this.data.type === 'new' && !product.tags.includes('新品')) {
        return false
      }
      
      // 关键词搜索
      if (this.data.keyword && !product.name.toLowerCase().includes(this.data.keyword.toLowerCase())) {
        return false
      }
      
      // 价格筛选
      if (this.data.minPrice && product.price < parseFloat(this.data.minPrice)) {
        return false
      }
      if (this.data.maxPrice && product.price > parseFloat(this.data.maxPrice)) {
        return false
      }
      
      // 标签筛选
      if (this.data.selectedTags.length > 0) {
        const hasMatchingTag = this.data.selectedTags.some(tag => 
          product.tags.includes(tag)
        )
        if (!hasMatchingTag) {
          return false
        }
      }
      
      return true
    })
  },

  // 应用排序
  applySorting(products) {
    const { currentSort } = this.data
    
    switch (currentSort) {
      case 'price_asc':
        return products.sort((a, b) => a.price - b.price)
      case 'price_desc':
        return products.sort((a, b) => b.price - a.price)
      case 'sales':
        return products.sort((a, b) => (b.sales || 0) - (a.sales || 0))
      case 'newest':
        return products.sort((a, b) => b.id - a.id)
      default:
        return products
    }
  },

  // 切换布局
  toggleLayout() {
    this.setData({
      layoutType: this.data.layoutType === 'grid' ? 'list' : 'grid'
    })
  },

  // 显示排序弹窗
  showSortModal() {
    this.setData({ showSort: true })
  },

  // 隐藏排序弹窗
  hideSortModal() {
    this.setData({ showSort: false })
  },

  // 选择排序方式
  selectSort(e) {
    const value = e.currentTarget.dataset.value
    const option = this.data.sortOptions.find(item => item.value === value)
    
    this.setData({
      currentSort: value,
      sortText: option.label,
      showSort: false
    })
    
    this.loadProducts(true)
  },

  // 显示筛选弹窗
  showFilterModal() {
    this.setData({ showFilter: true })
  },

  // 隐藏筛选弹窗
  hideFilterModal() {
    this.setData({ showFilter: false })
  },

  // 价格输入
  onMinPriceInput(e) {
    this.setData({ minPrice: e.detail.value })
  },

  onMaxPriceInput(e) {
    this.setData({ maxPrice: e.detail.value })
  },

  // 切换标签
  toggleTag(e) {
    const tag = e.currentTarget.dataset.tag
    const selectedTags = [...this.data.selectedTags]
    const index = selectedTags.indexOf(tag)
    
    if (index > -1) {
      selectedTags.splice(index, 1)
    } else {
      selectedTags.push(tag)
    }
    
    this.setData({ selectedTags })
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      minPrice: '',
      maxPrice: '',
      selectedTags: []
    })
  },

  // 应用筛选
  applyFilter() {
    this.setData({ showFilter: false })
    this.loadProducts(true)
  },

  // 商品点击
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    })
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loading) return
    this.loadProducts(false)
  }
})
