// index.js
Page({
  data: {
    banners: [],
    categories: [],
    hotProducts: [],
    newProducts: [],
    featuredProducts: []
  },

  onLoad() {
    this.initData()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadProducts()
  },

  // 初始化数据
  initData() {
    this.loadBanners()
    this.loadCategories()
    this.loadProducts()
  },

  // 加载轮播图数据
  loadBanners() {
    const banners = [
      {
        id: 1,
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop',
        url: '/pages/product-list/product-list?category=anime'
      },
      {
        id: 2,
        image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=800&h=400&fit=crop',
        url: '/pages/product-list/product-list?category=game'
      },
      {
        id: 3,
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop',
        url: '/pages/product-list/product-list?category=limited'
      }
    ]
    this.setData({ banners })
  },

  // 加载分类数据
  loadCategories() {
    const categories = [
      { id: 'anime', name: '动漫', icon: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop' },
      { id: 'game', name: '游戏', icon: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=100&h=100&fit=crop' },
      { id: 'movie', name: '影视', icon: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop' },
      { id: 'limited', name: '限定', icon: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=100&h=100&fit=crop' }
    ]
    this.setData({ categories })
  },

  // 加载商品数据
  async loadProducts() {
    try {
      // 模拟数据，实际应该从云数据库获取
      const hotProducts = [
        {
          id: 1,
          name: '初音未来 雪初音版',
          price: 299.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop'
        },
        {
          id: 2,
          name: '海贼王 路飞 四档',
          price: 399.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop'
        },
        {
          id: 3,
          name: '鬼灭之刃 炭治郎',
          price: 259.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop'
        }
      ]

      const newProducts = [
        {
          id: 4,
          name: '原神 甘雨',
          price: 459.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop'
        },
        {
          id: 5,
          name: '明日方舟 德克萨斯',
          price: 329.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop'
        }
      ]

      const featuredProducts = [
        {
          id: 6,
          name: '高达 RX-78-2',
          description: '经典高达模型，精工细作',
          price: 599.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=400&h=300&fit=crop'
        },
        {
          id: 7,
          name: '新世纪福音战士 初号机',
          description: '限量版收藏手办',
          price: 899.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop'
        }
      ]

      this.setData({
        hotProducts,
        newProducts,
        featuredProducts
      })
    } catch (error) {
      console.error('加载商品数据失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const url = e.currentTarget.dataset.url
    if (url) {
      wx.navigateTo({ url })
    }
  },

  // 分类点击事件
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product-list/product-list?category=${categoryId}`
    })
  },

  // 商品点击事件
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    })
  },

  // 更多按钮点击事件
  onMoreTap(e) {
    const type = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/pages/product-list/product-list?type=${type}`
    })
  }
})