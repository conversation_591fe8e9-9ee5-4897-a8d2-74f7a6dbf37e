// admin.js
Page({
  data: {
    currentDate: '',
    stats: {
      todayOrders: 0,
      todayRevenue: '0.00',
      totalProducts: 0,
      totalUsers: 0
    },
    pendingTasks: {
      unpaidOrders: 0,
      unshippedOrders: 0,
      lowStockProducts: 0,
      refundRequests: 0
    },
    recentOrders: []
  },

  onLoad() {
    this.initData()
    this.loadDashboardData()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadDashboardData()
  },

  // 初始化数据
  initData() {
    const now = new Date()
    const currentDate = `${now.getMonth() + 1}月${now.getDate()}日`
    this.setData({ currentDate })
  },

  // 加载仪表盘数据
  async loadDashboardData() {
    try {
      // 这里应该调用云函数获取统计数据
      // const result = await wx.cloud.callFunction({
      //   name: 'getAdminDashboard',
      //   data: {}
      // })

      // 模拟数据
      const mockStats = {
        todayOrders: 28,
        todayRevenue: '15,680.50',
        totalProducts: 156,
        totalUsers: 1248
      }

      const mockPendingTasks = {
        unpaidOrders: 5,
        unshippedOrders: 12,
        lowStockProducts: 3,
        refundRequests: 2
      }

      const mockRecentOrders = [
        {
          id: 'ORDER001',
          orderNumber: 'SB202412240001',
          customerName: '张三',
          amount: '299.00',
          createTime: '12:30',
          status: 'paid',
          statusText: '待发货'
        },
        {
          id: 'ORDER002',
          orderNumber: 'SB202412240002',
          customerName: '李四',
          amount: '158.00',
          createTime: '11:45',
          status: 'pending',
          statusText: '待付款'
        },
        {
          id: 'ORDER003',
          orderNumber: 'SB202412240003',
          customerName: '王五',
          amount: '89.00',
          createTime: '10:20',
          status: 'shipped',
          statusText: '已发货'
        },
        {
          id: 'ORDER004',
          orderNumber: 'SB202412240004',
          customerName: '赵六',
          amount: '456.00',
          createTime: '09:15',
          status: 'completed',
          statusText: '已完成'
        }
      ]

      this.setData({
        stats: mockStats,
        pendingTasks: mockPendingTasks,
        recentOrders: mockRecentOrders
      })
    } catch (error) {
      console.error('加载仪表盘数据失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 跳转到商品管理
  goToProductManage(e) {
    const filter = e.currentTarget.dataset.filter || ''
    wx.navigateTo({
      url: `/pages/admin-products/admin-products?filter=${filter}`
    })
  },

  // 跳转到订单管理
  goToOrderManage(e) {
    const status = e.currentTarget.dataset.status || 'all'
    wx.navigateTo({
      url: `/pages/admin-orders/admin-orders?status=${status}`
    })
  },

  // 跳转到用户管理
  goToUserManage() {
    wx.navigateTo({
      url: '/pages/admin-users/admin-users'
    })
  },

  // 跳转到数据分析
  goToDataAnalysis() {
    wx.navigateTo({
      url: '/pages/admin-analytics/admin-analytics'
    })
  },

  // 跳转到订单详情
  goToOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}&from=admin`
    })
  },

  // 跳转到系统设置
  goToSettings() {
    wx.navigateTo({
      url: '/pages/admin-settings/admin-settings'
    })
  },

  // 跳转到数据备份
  goToBackup() {
    wx.showModal({
      title: '数据备份',
      content: '确定要执行数据备份操作吗？',
      success: (res) => {
        if (res.confirm) {
          this.performBackup()
        }
      }
    })
  },

  // 执行备份
  async performBackup() {
    wx.showLoading({
      title: '备份中...'
    })

    try {
      // 这里应该调用云函数执行备份
      // await wx.cloud.callFunction({
      //   name: 'performDataBackup',
      //   data: {}
      // })

      // 模拟备份过程
      await new Promise(resolve => setTimeout(resolve, 2000))

      wx.hideLoading()
      wx.showToast({
        title: '备份完成',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      console.error('数据备份失败：', error)
      wx.showToast({
        title: '备份失败',
        icon: 'none'
      })
    }
  },

  // 跳转到操作日志
  goToLogs() {
    wx.navigateTo({
      url: '/pages/admin-logs/admin-logs'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDashboardData().then(() => {
      wx.stopPullDownRefresh()
    })
  }
})
