// category.js
Page({
  data: {
    categories: [],
    currentCategory: 'anime',
    products: [],
    searchKeyword: '',
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20
  },

  onLoad(options) {
    this.initData()
    // 如果从首页跳转过来带有分类参数
    if (options.category) {
      this.setData({
        currentCategory: options.category
      })
    }
  },

  onShow() {
    this.loadProducts()
  },

  // 初始化数据
  initData() {
    this.loadCategories()
    this.loadProducts()
  },

  // 加载分类数据
  loadCategories() {
    const categories = [
      { id: 'anime', name: '动漫手办' },
      { id: 'game', name: '游戏手办' },
      { id: 'movie', name: '影视手办' },
      { id: 'gundam', name: '高达模型' },
      { id: 'nendoroid', name: '粘土人' },
      { id: 'figma', name: 'figma' },
      { id: 'scale', name: '比例手办' },
      { id: 'limited', name: '限定版' },
      { id: 'garage', name: '车库套件' },
      { id: 'blind', name: '盲盒' }
    ]
    this.setData({ categories })
  },

  // 加载商品数据
  async loadProducts(reset = true) {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    try {
      // 模拟数据，实际应该从云数据库获取
      const mockProducts = [
        {
          id: 1,
          name: '初音未来 雪初音版 1/8 完成品手办',
          price: 299.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          category: 'anime',
          tags: ['热销', '现货']
        },
        {
          id: 2,
          name: '海贼王 路飞 四档 战斗版',
          price: 399.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop',
          category: 'anime',
          tags: ['新品', '预售']
        },
        {
          id: 3,
          name: '鬼灭之刃 炭治郎 DX版',
          price: 259.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          category: 'anime',
          tags: ['限定']
        },
        {
          id: 4,
          name: '原神 甘雨 1/7 完成品',
          price: 459.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop',
          category: 'game',
          tags: ['热销']
        },
        {
          id: 5,
          name: '明日方舟 德克萨斯',
          price: 329.00,
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          category: 'game',
          tags: ['现货']
        },
        {
          id: 6,
          name: '高达 RX-78-2 Ver.Ka',
          price: 599.00,
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop',
          category: 'gundam',
          tags: ['经典', '现货']
        }
      ]

      // 根据当前分类和搜索关键词过滤商品
      let filteredProducts = mockProducts.filter(product => {
        const matchCategory = product.category === this.data.currentCategory
        const matchKeyword = !this.data.searchKeyword || 
          product.name.toLowerCase().includes(this.data.searchKeyword.toLowerCase())
        return matchCategory && matchKeyword
      })

      if (reset) {
        this.setData({
          products: filteredProducts,
          page: 1,
          hasMore: filteredProducts.length >= this.data.pageSize
        })
      } else {
        this.setData({
          products: [...this.data.products, ...filteredProducts],
          page: this.data.page + 1,
          hasMore: filteredProducts.length >= this.data.pageSize
        })
      }
    } catch (error) {
      console.error('加载商品失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 分类切换
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id
    if (categoryId !== this.data.currentCategory) {
      this.setData({
        currentCategory: categoryId,
        searchKeyword: '',
        page: 1
      })
      this.loadProducts(true)
    }
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 搜索
  onSearch() {
    this.loadProducts(true)
  },

  // 商品点击
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    })
  },

  // 加载更多
  loadMore() {
    if (!this.data.hasMore || this.data.loading) return
    this.loadProducts(false)
  }
})
