<!--order.wxml-->
<view class="container">
  <!-- 收货地址 -->
  <view class="address-section" bindtap="selectAddress">
    <view class="address-content" wx:if="{{selectedAddress}}">
      <view class="address-header">
        <text class="receiver-name">{{selectedAddress.name}}</text>
        <text class="receiver-phone">{{selectedAddress.phone}}</text>
      </view>
      <text class="address-detail">{{selectedAddress.province}} {{selectedAddress.city}} {{selectedAddress.district}} {{selectedAddress.detail}}</text>
    </view>
    <view class="no-address" wx:else>
      <text class="no-address-text">请选择收货地址</text>
    </view>
    <text class="address-arrow">></text>
  </view>

  <!-- 商品列表 -->
  <view class="product-section">
    <view class="section-header">
      <text class="section-title">商品清单</text>
    </view>
    <view class="product-list">
      <view class="product-item" wx:for="{{orderData.products}}" wx:key="id">
        <image src="{{item.image}}" class="product-image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-spec" wx:if="{{item.spec}}">{{item.spec}}</text>
          <view class="product-price-quantity">
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.price}}</text>
            </view>
            <text class="product-quantity">x{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 配送方式 -->
  <view class="delivery-section">
    <view class="section-header">
      <text class="section-title">配送方式</text>
    </view>
    <view class="delivery-options">
      <view 
        class="delivery-option {{selectedDelivery === item.id ? 'active' : ''}}" 
        wx:for="{{deliveryOptions}}" 
        wx:key="id"
        bindtap="selectDelivery" 
        data-id="{{item.id}}"
      >
        <view class="delivery-info">
          <text class="delivery-name">{{item.name}}</text>
          <text class="delivery-desc">{{item.desc}}</text>
        </view>
        <text class="delivery-price">{{item.price > 0 ? '¥' + item.price : '免费'}}</text>
      </view>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-section">
    <view class="section-header">
      <text class="section-title">支付方式</text>
    </view>
    <view class="payment-options">
      <view 
        class="payment-option {{selectedPayment === item.id ? 'active' : ''}}" 
        wx:for="{{paymentOptions}}" 
        wx:key="id"
        bindtap="selectPayment" 
        data-id="{{item.id}}"
      >
        <image src="{{item.icon}}" class="payment-icon"></image>
        <text class="payment-name">{{item.name}}</text>
        <text class="payment-check" wx:if="{{selectedPayment === item.id}}">✓</text>
      </view>
    </view>
  </view>

  <!-- 订单备注 -->
  <view class="remark-section">
    <view class="section-header">
      <text class="section-title">订单备注</text>
    </view>
    <textarea 
      class="remark-input" 
      placeholder="选填，请输入订单备注信息"
      value="{{remark}}"
      bindinput="onRemarkInput"
      maxlength="200"
    ></textarea>
  </view>

  <!-- 优惠券 -->
  <view class="coupon-section" bindtap="selectCoupon">
    <view class="coupon-content">
      <text class="coupon-title">优惠券</text>
      <view class="coupon-info">
        <text class="coupon-text" wx:if="{{selectedCoupon}}">-¥{{selectedCoupon.amount}}</text>
        <text class="coupon-text" wx:else>暂无可用</text>
      </view>
    </view>
    <text class="coupon-arrow">></text>
  </view>

  <!-- 费用明细 -->
  <view class="cost-section">
    <view class="cost-item">
      <text class="cost-label">商品总价</text>
      <text class="cost-value">¥{{orderData.totalAmount}}</text>
    </view>
    <view class="cost-item">
      <text class="cost-label">运费</text>
      <text class="cost-value">{{deliveryFee > 0 ? '¥' + deliveryFee : '免费'}}</text>
    </view>
    <view class="cost-item" wx:if="{{couponDiscount > 0}}">
      <text class="cost-label">优惠券</text>
      <text class="cost-value discount">-¥{{couponDiscount}}</text>
    </view>
    <view class="cost-item total">
      <text class="cost-label">实付款</text>
      <text class="cost-value total-amount">¥{{finalAmount}}</text>
    </view>
  </view>

  <!-- 底部提交按钮 -->
  <view class="bottom-bar">
    <view class="total-info">
      <text class="total-label">实付：</text>
      <text class="total-symbol">¥</text>
      <text class="total-value">{{finalAmount}}</text>
    </view>
    <button 
      class="submit-btn {{canSubmit ? 'active' : ''}}" 
      bindtap="submitOrder"
      disabled="{{!canSubmit}}"
    >
      提交订单
    </button>
  </view>

  <!-- 地址选择弹窗 -->
  <view class="modal-mask" wx:if="{{showAddressModal}}" bindtap="hideAddressModal">
    <view class="address-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择收货地址</text>
        <text class="modal-close" bindtap="hideAddressModal">×</text>
      </view>
      <view class="address-list">
        <view 
          class="address-item {{selectedAddress && selectedAddress.id === item.id ? 'active' : ''}}" 
          wx:for="{{addressList}}" 
          wx:key="id"
          bindtap="chooseAddress" 
          data-index="{{index}}"
        >
          <view class="address-info">
            <view class="address-header">
              <text class="receiver-name">{{item.name}}</text>
              <text class="receiver-phone">{{item.phone}}</text>
              <text class="default-tag" wx:if="{{item.isDefault}}">默认</text>
            </view>
            <text class="address-detail">{{item.province}} {{item.city}} {{item.district}} {{item.detail}}</text>
          </view>
          <text class="address-check" wx:if="{{selectedAddress && selectedAddress.id === item.id}}">✓</text>
        </view>
      </view>
      <view class="address-actions">
        <button class="add-address-btn" bindtap="addAddress">新增地址</button>
      </view>
    </view>
  </view>
</view>
