/* order.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 通用样式 */
.section-header {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 收货地址 */
.address-section {
  background: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.address-content {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.receiver-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.receiver-phone {
  font-size: 26rpx;
  color: #666;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: block;
}

.no-address {
  flex: 1;
}

.no-address-text {
  font-size: 26rpx;
  color: #999;
}

.address-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 商品列表 */
.product-section {
  background: white;
  margin-bottom: 20rpx;
}

.product-list {
  padding: 0 30rpx 20rpx;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 1.4;
}

.product-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: block;
}

.product-price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 22rpx;
  color: #FF6B9D;
}

.price-value {
  font-size: 26rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.product-quantity {
  font-size: 24rpx;
  color: #666;
}

/* 配送方式 */
.delivery-section {
  background: white;
  margin-bottom: 20rpx;
}

.delivery-options {
  padding: 0 30rpx 20rpx;
}

.delivery-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.delivery-option:last-child {
  border-bottom: none;
}

.delivery-option.active {
  color: #FF6B9D;
}

.delivery-info {
  flex: 1;
}

.delivery-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
  display: block;
}

.delivery-option.active .delivery-name {
  color: #FF6B9D;
}

.delivery-desc {
  font-size: 22rpx;
  color: #999;
  display: block;
}

.delivery-price {
  font-size: 24rpx;
  color: #666;
}

.delivery-option.active .delivery-price {
  color: #FF6B9D;
}

/* 支付方式 */
.payment-section {
  background: white;
  margin-bottom: 20rpx;
}

.payment-options {
  padding: 0 30rpx 20rpx;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.payment-option:last-child {
  border-bottom: none;
}

.payment-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.payment-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.payment-check {
  font-size: 24rpx;
  color: #FF6B9D;
  font-weight: bold;
}

/* 订单备注 */
.remark-section {
  background: white;
  margin-bottom: 20rpx;
}

.remark-input {
  margin: 0 30rpx 20rpx;
  padding: 20rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  font-size: 26rpx;
  min-height: 120rpx;
}

/* 优惠券 */
.coupon-section {
  background: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
}

.coupon-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-title {
  font-size: 26rpx;
  color: #333;
}

.coupon-text {
  font-size: 24rpx;
  color: #FF6B9D;
}

.coupon-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}

/* 费用明细 */
.cost-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.cost-item:last-child {
  margin-bottom: 0;
}

.cost-item.total {
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
  font-weight: bold;
}

.cost-label {
  font-size: 26rpx;
  color: #666;
}

.cost-item.total .cost-label {
  color: #333;
  font-size: 28rpx;
}

.cost-value {
  font-size: 26rpx;
  color: #333;
}

.cost-value.discount {
  color: #FF6B9D;
}

.cost-value.total-amount {
  color: #FF6B9D;
  font-size: 32rpx;
  font-weight: bold;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  z-index: 100;
}

.total-info {
  flex: 1;
  text-align: right;
  margin-right: 20rpx;
}

.total-label {
  font-size: 24rpx;
  color: #666;
}

.total-symbol {
  font-size: 24rpx;
  color: #FF6B9D;
}

.total-value {
  font-size: 32rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.submit-btn {
  background-color: #ccc;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.submit-btn.active {
  background-color: #FF6B9D;
}

.submit-btn[disabled] {
  background-color: #ccc;
}

/* 地址选择弹窗 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.address-modal {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-list {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 0;
}

.address-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.address-item:last-child {
  border-bottom: none;
}

.address-item.active {
  background-color: #fff0f5;
}

.address-info {
  flex: 1;
}

.address-item .address-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.address-item .receiver-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}

.address-item .receiver-phone {
  font-size: 24rpx;
  color: #666;
  margin-right: 15rpx;
}

.default-tag {
  background-color: #FF6B9D;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.address-item .address-detail {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.address-check {
  font-size: 24rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.address-actions {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.add-address-btn {
  width: 100%;
  height: 80rpx;
  background-color: #FF6B9D;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}
