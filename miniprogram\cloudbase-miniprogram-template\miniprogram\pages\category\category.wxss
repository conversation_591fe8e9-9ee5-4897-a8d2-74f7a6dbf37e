/* category.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

/* 搜索框样式 */
.search-bar {
  display: flex;
  padding: 20rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 35rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
}

.search-btn {
  width: 120rpx;
  height: 70rpx;
  margin-left: 20rpx;
  background-color: #FF6B9D;
  color: white;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
  line-height: 70rpx;
}

/* 内容区域 */
.content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧分类列表 */
.category-list {
  width: 200rpx;
  background: white;
  border-right: 1rpx solid #eee;
}

.category-item {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.category-item.active {
  background-color: #fff0f5;
  color: #FF6B9D;
}

.category-item.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 40rpx;
  background-color: #FF6B9D;
  border-radius: 3rpx 0 0 3rpx;
}

.category-name {
  font-size: 26rpx;
  text-align: center;
}

/* 右侧商品列表 */
.product-list {
  flex: 1;
  background-color: #f8f8f8;
}

.product-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  gap: 20rpx;
}

.product-item {
  width: calc(50% - 10rpx);
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 1.4;
}

.product-price {
  color: #FF6B9D;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.price-symbol {
  font-size: 24rpx;
}

.price-value {
  font-size: 28rpx;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.tag {
  padding: 4rpx 12rpx;
  background-color: #FF6B9D;
  color: white;
  font-size: 20rpx;
  border-radius: 10rpx;
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-more-btn {
  width: 300rpx;
  height: 80rpx;
  background-color: #FF6B9D;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.no-more {
  padding: 40rpx;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}
