<!--index.wxml-->
<view class="container">
  <!-- 轮播图 -->
  <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image src="{{item.image}}" class="banner-image" mode="aspectFill" bindtap="onBannerTap" data-url="{{item.url}}"></image>
    </swiper-item>
  </swiper>

  <!-- 分类导航 -->
  <view class="category-nav">
    <view class="nav-item" wx:for="{{categories}}" wx:key="id" bindtap="onCategoryTap" data-id="{{item.id}}">
      <image src="{{item.icon}}" class="nav-icon"></image>
      <text class="nav-text">{{item.name}}</text>
    </view>
  </view>

  <!-- 热销推荐 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">🔥 热销推荐</text>
      <text class="section-more" bindtap="onMoreTap" data-type="hot">更多 ></text>
    </view>
    <scroll-view class="product-scroll" scroll-x="true">
      <view class="product-item" wx:for="{{hotProducts}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
        <image src="{{item.image}}" class="product-image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 新品上架 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">✨ 新品上架</text>
      <text class="section-more" bindtap="onMoreTap" data-type="new">更多 ></text>
    </view>
    <scroll-view class="product-scroll" scroll-x="true">
      <view class="product-item" wx:for="{{newProducts}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
        <image src="{{item.image}}" class="product-image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 精选手办 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">💎 精选手办</text>
    </view>
    <view class="featured-grid">
      <view class="featured-item" wx:for="{{featuredProducts}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
        <image src="{{item.image}}" class="featured-image" mode="aspectFill"></image>
        <view class="featured-info">
          <text class="featured-name">{{item.name}}</text>
          <text class="featured-desc">{{item.description}}</text>
          <view class="featured-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>