// user.js
Page({
  data: {
    userInfo: null,
    isAdmin: false,
    orderStats: {
      total: 0,
      pending: 0,
      paid: 0,
      shipped: 0,
      completed: 0,
      refund: 0
    },
    favoriteCount: 0,
    couponCount: 0
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (this.data.userInfo) {
      this.loadUserData()
    }
  },

  // 检查登录状态
  async checkLoginStatus() {
    try {
      // 这里应该检查用户登录状态
      // const loginState = await wx.cloud.auth().getLoginState()
      
      // 模拟已登录用户
      const mockUserInfo = {
        avatarUrl: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
        nickName: '手办爱好者',
        openId: 'mock_openid'
      }
      
      this.setData({ 
        userInfo: mockUserInfo,
        isAdmin: false // 可以根据用户权限设置
      })
      
      this.loadUserData()
    } catch (error) {
      console.error('检查登录状态失败：', error)
    }
  },

  // 加载用户数据
  async loadUserData() {
    try {
      // 加载订单统计
      await this.loadOrderStats()
      // 加载收藏数量
      await this.loadFavoriteCount()
      // 加载优惠券数量
      await this.loadCouponCount()
    } catch (error) {
      console.error('加载用户数据失败：', error)
    }
  },

  // 加载订单统计
  async loadOrderStats() {
    try {
      // 这里应该调用云函数获取订单统计
      // const result = await wx.cloud.callFunction({
      //   name: 'getUserOrderStats',
      //   data: {}
      // })

      // 模拟数据
      const mockOrderStats = {
        total: 15,
        pending: 2,
        paid: 1,
        shipped: 3,
        completed: 8,
        refund: 1
      }

      this.setData({ orderStats: mockOrderStats })
    } catch (error) {
      console.error('加载订单统计失败：', error)
    }
  },

  // 加载收藏数量
  async loadFavoriteCount() {
    try {
      // 模拟数据
      this.setData({ favoriteCount: 12 })
    } catch (error) {
      console.error('加载收藏数量失败：', error)
    }
  },

  // 加载优惠券数量
  async loadCouponCount() {
    try {
      // 模拟数据
      this.setData({ couponCount: 5 })
    } catch (error) {
      console.error('加载优惠券数量失败：', error)
    }
  },

  // 用户登录
  async login() {
    try {
      // 获取用户信息
      const userProfile = await wx.getUserProfile({
        desc: '用于完善用户资料'
      })

      // 这里应该调用云函数进行登录
      // const loginResult = await wx.cloud.callFunction({
      //   name: 'userLogin',
      //   data: {
      //     userInfo: userProfile.userInfo
      //   }
      // })

      this.setData({ 
        userInfo: userProfile.userInfo 
      })

      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })

      this.loadUserData()
    } catch (error) {
      console.error('登录失败：', error)
      if (error.errMsg !== 'getUserProfile:fail auth deny') {
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    }
  },

  // 更新头像
  async updateAvatar() {
    try {
      const result = await wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera']
      })

      if (result.tempFiles && result.tempFiles.length > 0) {
        const tempFilePath = result.tempFiles[0].tempFilePath

        wx.showLoading({
          title: '上传中...'
        })

        // 这里应该上传到云存储
        // const uploadResult = await wx.cloud.uploadFile({
        //   cloudPath: `avatars/${Date.now()}.jpg`,
        //   filePath: tempFilePath
        // })

        // 更新用户信息
        const userInfo = { ...this.data.userInfo }
        userInfo.avatarUrl = tempFilePath // 实际应该使用云存储的URL

        this.setData({ userInfo })

        wx.hideLoading()
        wx.showToast({
          title: '更新成功',
          icon: 'success'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('更新头像失败：', error)
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    }
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            userInfo: null,
            isAdmin: false,
            orderStats: {
              total: 0,
              pending: 0,
              paid: 0,
              shipped: 0,
              completed: 0,
              refund: 0
            },
            favoriteCount: 0,
            couponCount: 0
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 跳转到订单页面
  goToOrders(e) {
    const status = e.currentTarget.dataset.status || 'all'
    wx.navigateTo({
      url: `/pages/order-list/order-list?status=${status}`
    })
  },

  // 跳转到收藏页面
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/favorite-list/favorite-list'
    })
  },

  // 跳转到优惠券页面
  goToCoupons() {
    wx.navigateTo({
      url: '/pages/coupon-list/coupon-list'
    })
  },

  // 跳转到地址管理
  goToAddresses() {
    wx.navigateTo({
      url: '/pages/address/address'
    })
  },

  // 跳转到客服中心
  goToCustomerService() {
    wx.navigateTo({
      url: '/pages/customer-service/customer-service'
    })
  },

  // 跳转到帮助中心
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/help'
    })
  },

  // 跳转到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 跳转到管理后台
  goToAdmin() {
    wx.navigateTo({
      url: '/pages/admin/admin'
    })
  }
})
