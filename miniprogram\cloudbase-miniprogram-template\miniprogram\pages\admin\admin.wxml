<!--admin.wxml-->
<view class="container">
  <!-- 统计概览 -->
  <view class="stats-section">
    <view class="stats-header">
      <text class="stats-title">数据概览</text>
      <text class="stats-date">今日 {{currentDate}}</text>
    </view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{stats.todayOrders}}</text>
        <text class="stat-label">今日订单</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">¥{{stats.todayRevenue}}</text>
        <text class="stat-label">今日营收</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalProducts}}</text>
        <text class="stat-label">商品总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{stats.totalUsers}}</text>
        <text class="stat-label">用户总数</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="section-title">快捷操作</view>
    <view class="action-grid">
      <view class="action-item" bindtap="goToProductManage">
        <image src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=60&h=60&fit=crop" class="action-icon"></image>
        <text class="action-text">商品管理</text>
      </view>
      <view class="action-item" bindtap="goToOrderManage">
        <image src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=60&h=60&fit=crop" class="action-icon"></image>
        <text class="action-text">订单管理</text>
      </view>
      <view class="action-item" bindtap="goToUserManage">
        <image src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=60&h=60&fit=crop&crop=face" class="action-icon"></image>
        <text class="action-text">用户管理</text>
      </view>
      <view class="action-item" bindtap="goToDataAnalysis">
        <image src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=60&h=60&fit=crop" class="action-icon"></image>
        <text class="action-text">数据分析</text>
      </view>
    </view>
  </view>

  <!-- 待处理事项 -->
  <view class="pending-section">
    <view class="section-title">待处理事项</view>
    <view class="pending-list">
      <view class="pending-item" bindtap="goToOrderManage" data-status="pending">
        <view class="pending-info">
          <text class="pending-title">待付款订单</text>
          <text class="pending-desc">需要跟进的未付款订单</text>
        </view>
        <view class="pending-count">{{pendingTasks.unpaidOrders}}</view>
      </view>
      <view class="pending-item" bindtap="goToOrderManage" data-status="paid">
        <view class="pending-info">
          <text class="pending-title">待发货订单</text>
          <text class="pending-desc">需要安排发货的订单</text>
        </view>
        <view class="pending-count">{{pendingTasks.unshippedOrders}}</view>
      </view>
      <view class="pending-item" bindtap="goToProductManage" data-filter="lowStock">
        <view class="pending-info">
          <text class="pending-title">库存不足</text>
          <text class="pending-desc">库存低于10件的商品</text>
        </view>
        <view class="pending-count">{{pendingTasks.lowStockProducts}}</view>
      </view>
      <view class="pending-item" bindtap="goToOrderManage" data-status="refund">
        <view class="pending-info">
          <text class="pending-title">退款申请</text>
          <text class="pending-desc">需要处理的退款申请</text>
        </view>
        <view class="pending-count">{{pendingTasks.refundRequests}}</view>
      </view>
    </view>
  </view>

  <!-- 最近订单 -->
  <view class="recent-orders">
    <view class="section-header">
      <text class="section-title">最近订单</text>
      <text class="view-all" bindtap="goToOrderManage">查看全部 ></text>
    </view>
    <view class="order-list">
      <view class="order-item" wx:for="{{recentOrders}}" wx:key="id" bindtap="goToOrderDetail" data-id="{{item.id}}">
        <view class="order-info">
          <text class="order-number">订单号：{{item.orderNumber}}</text>
          <text class="order-time">{{item.createTime}}</text>
        </view>
        <view class="order-details">
          <text class="order-customer">{{item.customerName}}</text>
          <text class="order-amount">¥{{item.amount}}</text>
        </view>
        <view class="order-status {{item.status}}">{{item.statusText}}</view>
      </view>
    </view>
  </view>

  <!-- 系统设置 -->
  <view class="system-section">
    <view class="section-title">系统设置</view>
    <view class="setting-list">
      <view class="setting-item" bindtap="goToSettings">
        <image src="https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=50&h=50&fit=crop" class="setting-icon"></image>
        <text class="setting-text">系统配置</text>
        <text class="setting-arrow">></text>
      </view>
      <view class="setting-item" bindtap="goToBackup">
        <image src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=50&h=50&fit=crop" class="setting-icon"></image>
        <text class="setting-text">数据备份</text>
        <text class="setting-arrow">></text>
      </view>
      <view class="setting-item" bindtap="goToLogs">
        <image src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=50&h=50&fit=crop" class="setting-icon"></image>
        <text class="setting-text">操作日志</text>
        <text class="setting-arrow">></text>
      </view>
    </view>
  </view>
</view>
