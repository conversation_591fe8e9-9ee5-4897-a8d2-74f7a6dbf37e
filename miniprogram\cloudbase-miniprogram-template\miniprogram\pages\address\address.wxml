<!--address.wxml-->
<view class="container">
  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{addressList.length > 0}}">
    <view class="address-item" wx:for="{{addressList}}" wx:key="id">
      <view class="address-content" bindtap="editAddress" data-id="{{item.id}}">
        <view class="address-header">
          <text class="receiver-name">{{item.name}}</text>
          <text class="receiver-phone">{{item.phone}}</text>
          <text class="default-tag" wx:if="{{item.isDefault}}">默认</text>
        </view>
        <text class="address-detail">{{item.province}} {{item.city}} {{item.district}} {{item.detail}}</text>
      </view>
      <view class="address-actions">
        <button class="action-btn" bindtap="setDefault" data-id="{{item.id}}" wx:if="{{!item.isDefault}}">设为默认</button>
        <button class="action-btn edit-btn" bindtap="editAddress" data-id="{{item.id}}">编辑</button>
        <button class="action-btn delete-btn" bindtap="deleteAddress" data-id="{{item.id}}">删除</button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=200&h=200&fit=crop" class="empty-image"></image>
    <text class="empty-text">暂无收货地址</text>
    <text class="empty-desc">添加收货地址，享受便捷购物体验</text>
  </view>

  <!-- 添加地址按钮 -->
  <view class="add-address-section">
    <button class="add-address-btn" bindtap="addAddress">
      <text class="add-icon">+</text>
      <text class="add-text">新增收货地址</text>
    </button>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-mask" wx:if="{{showDeleteModal}}" bindtap="hideDeleteModal">
    <view class="delete-modal" catchtap="">
      <view class="modal-title">确认删除</view>
      <view class="modal-content">确定要删除这个收货地址吗？</view>
      <view class="modal-actions">
        <button class="modal-cancel" bindtap="hideDeleteModal">取消</button>
        <button class="modal-confirm" bindtap="confirmDelete">确定</button>
      </view>
    </view>
  </view>
</view>
