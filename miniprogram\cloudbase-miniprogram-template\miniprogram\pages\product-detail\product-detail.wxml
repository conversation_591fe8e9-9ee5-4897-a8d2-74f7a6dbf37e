<!--product-detail.wxml-->
<view class="container">
  <!-- 商品图片轮播 -->
  <swiper class="product-swiper" indicator-dots="{{true}}" autoplay="{{false}}">
    <swiper-item wx:for="{{product.images}}" wx:key="*this">
      <image src="{{item}}" class="product-image" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
    </swiper-item>
  </swiper>

  <!-- 商品基本信息 -->
  <view class="product-info">
    <view class="product-header">
      <text class="product-name">{{product.name}}</text>
      <view class="product-tags">
        <text class="tag" wx:for="{{product.tags}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
    
    <view class="price-section">
      <view class="current-price">
        <text class="price-symbol">¥</text>
        <text class="price-value">{{product.price}}</text>
      </view>
      <view class="original-price" wx:if="{{product.originalPrice}}">
        <text>原价：¥{{product.originalPrice}}</text>
      </view>
    </view>
    
    <view class="product-stats">
      <view class="stat-item">
        <text class="stat-label">销量</text>
        <text class="stat-value">{{product.sales || 0}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">库存</text>
        <text class="stat-value">{{product.stock || 0}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">评分</text>
        <text class="stat-value">★{{product.rating || 5.0}}</text>
      </view>
    </view>
  </view>

  <!-- 规格选择 -->
  <view class="spec-section" wx:if="{{product.specs && product.specs.length > 0}}">
    <view class="section-title">选择规格</view>
    <view class="spec-options">
      <view 
        class="spec-option {{selectedSpec === item.id ? 'active' : ''}}" 
        wx:for="{{product.specs}}" 
        wx:key="id"
        bindtap="selectSpec" 
        data-id="{{item.id}}"
      >
        <text>{{item.name}}</text>
        <text class="spec-price">+¥{{item.price}}</text>
      </view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="detail-section">
    <view class="section-title">商品详情</view>
    <view class="detail-content">
      <text class="detail-text">{{product.description}}</text>
      <view class="detail-images" wx:if="{{product.detailImages && product.detailImages.length > 0}}">
        <image 
          wx:for="{{product.detailImages}}" 
          wx:key="*this" 
          src="{{item}}" 
          class="detail-image" 
          mode="widthFix"
          bindtap="previewImage" 
          data-url="{{item}}"
        ></image>
      </view>
    </view>
  </view>

  <!-- 用户评价 -->
  <view class="review-section">
    <view class="section-header">
      <text class="section-title">用户评价 ({{reviews.length}})</text>
      <text class="view-all" bindtap="viewAllReviews">查看全部 ></text>
    </view>
    <view class="review-list">
      <view class="review-item" wx:for="{{reviews}}" wx:key="id" wx:if="{{index < 3}}">
        <view class="review-header">
          <image src="{{item.avatar}}" class="user-avatar"></image>
          <view class="user-info">
            <text class="username">{{item.username}}</text>
            <view class="rating">
              <text class="stars">{{item.stars}}</text>
              <text class="review-date">{{item.date}}</text>
            </view>
          </view>
        </view>
        <text class="review-content">{{item.content}}</text>
        <view class="review-images" wx:if="{{item.images && item.images.length > 0}}">
          <image 
            wx:for="{{item.images}}" 
            wx:key="*this" 
            wx:for-item="img"
            src="{{img}}" 
            class="review-image" 
            mode="aspectFill"
            bindtap="previewImage" 
            data-url="{{img}}"
          ></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="action-buttons">
      <button class="btn-cart" bindtap="addToCart">
        <text class="btn-text">加入购物车</text>
      </button>
      <button class="btn-buy" bindtap="buyNow">
        <text class="btn-text">立即购买</text>
      </button>
    </view>
  </view>

  <!-- 数量选择弹窗 -->
  <view class="modal-mask" wx:if="{{showQuantityModal}}" bindtap="hideQuantityModal">
    <view class="quantity-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">选择数量</text>
        <text class="modal-close" bindtap="hideQuantityModal">×</text>
      </view>
      <view class="quantity-section">
        <view class="quantity-info">
          <image src="{{product.images[0]}}" class="quantity-image"></image>
          <view class="quantity-details">
            <text class="quantity-name">{{product.name}}</text>
            <text class="quantity-price">¥{{product.price}}</text>
            <text class="quantity-stock">库存：{{product.stock}}件</text>
          </view>
        </view>
        <view class="quantity-control">
          <text class="quantity-label">数量</text>
          <view class="quantity-input">
            <button class="quantity-btn" bindtap="decreaseQuantity" disabled="{{quantity <= 1}}">-</button>
            <input class="quantity-value" type="number" value="{{quantity}}" bindinput="onQuantityInput" />
            <button class="quantity-btn" bindtap="increaseQuantity" disabled="{{quantity >= product.stock}}">+</button>
          </view>
        </view>
      </view>
      <view class="quantity-actions">
        <button class="quantity-confirm" bindtap="confirmQuantity">确定</button>
      </view>
    </view>
  </view>
</view>
