/* user.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 用户信息头部 */
.user-header {
  background: linear-gradient(135deg, #FF6B9D, #FF8FA3);
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 25rpx;
  border: 4rpx solid rgba(255,255,255,0.3);
}

.default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 25rpx;
  opacity: 0.7;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.user-level {
  font-size: 24rpx;
  opacity: 0.8;
  background-color: rgba(255,255,255,0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.login-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.login-info {
  flex: 1;
}

.login-text {
  font-size: 28rpx;
  margin-bottom: 15rpx;
  display: block;
}

.login-btn {
  background-color: rgba(255,255,255,0.2);
  color: white;
  border: 1rpx solid rgba(255,255,255,0.3);
  border-radius: 25rpx;
  padding: 10rpx 30rpx;
  font-size: 24rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
}

/* 订单状态 */
.order-section {
  background: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx 20rpx 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 0 10rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  font-size: 24rpx;
  color: #FF6B9D;
}

.order-status {
  display: flex;
  justify-content: space-around;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.status-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.status-text {
  font-size: 22rpx;
  color: #666;
}

.status-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #FF6B9D;
  color: white;
  font-size: 18rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
}

/* 功能菜单 */
.menu-section {
  margin: 20rpx;
}

.menu-group {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item.admin-item {
  background-color: #fff5f5;
}

.menu-item.admin-item .menu-text {
  color: #FF6B9D;
  font-weight: bold;
}

.menu-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  margin-right: 25rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 退出登录 */
.logout-section {
  margin: 20rpx;
  padding: 0 10rpx;
}

.logout-btn {
  width: 100%;
  height: 80rpx;
  background-color: white;
  color: #999;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.logout-btn:active {
  background-color: #f5f5f5;
}
