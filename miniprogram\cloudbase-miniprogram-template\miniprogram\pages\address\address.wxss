/* address.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

/* 地址列表 */
.address-list {
  padding: 20rpx;
}

.address-item {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.address-content {
  padding: 30rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.receiver-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.receiver-phone {
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
}

.default-tag {
  background-color: #FF6B9D;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
}

.address-detail {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

.address-actions {
  display: flex;
  border-top: 1rpx solid #f5f5f5;
  background-color: #fafafa;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  background: none;
  border: none;
  font-size: 26rpx;
  color: #666;
  border-right: 1rpx solid #f5f5f5;
}

.action-btn:last-child {
  border-right: none;
}

.action-btn:active {
  background-color: #f0f0f0;
}

.edit-btn {
  color: #FF6B9D;
}

.delete-btn {
  color: #ff4757;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.5;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 15rpx;
  display: block;
}

.empty-desc {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}

/* 添加地址按钮 */
.add-address-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
}

.add-address-btn {
  width: 100%;
  height: 80rpx;
  background-color: #FF6B9D;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.add-text {
  color: inherit;
}

/* 删除确认弹窗 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-modal {
  width: 500rpx;
  background: white;
  border-radius: 12rpx;
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.modal-content {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.modal-cancel,
.modal-confirm {
  flex: 1;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  font-size: 26rpx;
}

.modal-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-confirm {
  background-color: #FF6B9D;
  color: white;
}
