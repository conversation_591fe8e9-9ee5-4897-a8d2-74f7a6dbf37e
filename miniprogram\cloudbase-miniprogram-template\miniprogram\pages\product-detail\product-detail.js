// product-detail.js
Page({
  data: {
    product: {},
    reviews: [],
    selectedSpec: null,
    quantity: 1,
    showQuantityModal: false,
    actionType: '' // 'cart' 或 'buy'
  },

  onLoad(options) {
    if (options.id) {
      this.loadProductDetail(options.id)
      this.loadReviews(options.id)
    }
  },

  onShareAppMessage() {
    return {
      title: this.data.product.name,
      path: `/pages/product-detail/product-detail?id=${this.data.product.id}`,
      imageUrl: this.data.product.images ? this.data.product.images[0] : ''
    }
  },

  // 加载商品详情
  async loadProductDetail(productId) {
    try {
      // 模拟数据，实际应该从云数据库获取
      const mockProduct = {
        id: parseInt(productId),
        name: '初音未来 雪初音版 1/8 完成品手办',
        price: 299.00,
        originalPrice: 399.00,
        images: [
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=600&fit=crop',
          'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=600&h=600&fit=crop',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=600&fit=crop'
        ],
        tags: ['热销', '现货', '包邮'],
        sales: 1234,
        stock: 99,
        rating: 4.8,
        description: '这是一款精美的初音未来手办，采用高品质PVC材料制作，细节丰富，色彩鲜艳。适合收藏和展示。\n\n产品特点：\n• 1/8比例，高约20cm\n• PVC+ABS材质\n• 精细涂装工艺\n• 原厂正版授权\n• 附带专用底座',
        detailImages: [
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=800&h=600&fit=crop'
        ],
        specs: [
          { id: 1, name: '标准版', price: 0 },
          { id: 2, name: '豪华版', price: 100 },
          { id: 3, name: '限定版', price: 200 }
        ]
      }

      this.setData({ 
        product: mockProduct,
        selectedSpec: mockProduct.specs ? mockProduct.specs[0].id : null
      })

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: mockProduct.name.length > 10 ? mockProduct.name.substring(0, 10) + '...' : mockProduct.name
      })
    } catch (error) {
      console.error('加载商品详情失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 加载用户评价
  async loadReviews(productId) {
    try {
      // 模拟数据
      const mockReviews = [
        {
          id: 1,
          username: '手办爱好者',
          avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face',
          stars: '★★★★★',
          date: '2024-01-15',
          content: '质量很好，做工精细，颜色鲜艳，非常满意！',
          images: [
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=200&fit=crop'
          ]
        },
        {
          id: 2,
          username: '动漫迷小王',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          stars: '★★★★☆',
          date: '2024-01-10',
          content: '包装很好，物流很快，手办质量不错，推荐购买。',
          images: []
        },
        {
          id: 3,
          username: '收藏家',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
          stars: '★★★★★',
          date: '2024-01-08',
          content: '非常棒的手办，细节处理得很好，值得收藏！',
          images: []
        }
      ]

      this.setData({ reviews: mockReviews })
    } catch (error) {
      console.error('加载评价失败：', error)
    }
  },

  // 预览图片
  previewImage(e) {
    const url = e.currentTarget.dataset.url
    const urls = this.data.product.images || [url]
    
    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 选择规格
  selectSpec(e) {
    const specId = e.currentTarget.dataset.id
    this.setData({ selectedSpec: specId })
  },

  // 查看全部评价
  viewAllReviews() {
    // 跳转到评价列表页面
    wx.navigateTo({
      url: `/pages/review-list/review-list?productId=${this.data.product.id}`
    })
  },

  // 加入购物车
  addToCart() {
    this.setData({ 
      actionType: 'cart',
      showQuantityModal: true 
    })
  },

  // 立即购买
  buyNow() {
    this.setData({ 
      actionType: 'buy',
      showQuantityModal: true 
    })
  },

  // 显示数量选择弹窗
  showQuantityModal() {
    this.setData({ showQuantityModal: true })
  },

  // 隐藏数量选择弹窗
  hideQuantityModal() {
    this.setData({ showQuantityModal: false })
  },

  // 减少数量
  decreaseQuantity() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      })
    }
  },

  // 增加数量
  increaseQuantity() {
    if (this.data.quantity < this.data.product.stock) {
      this.setData({
        quantity: this.data.quantity + 1
      })
    }
  },

  // 数量输入
  onQuantityInput(e) {
    const value = parseInt(e.detail.value) || 1
    const maxQuantity = this.data.product.stock || 1
    
    this.setData({
      quantity: Math.max(1, Math.min(value, maxQuantity))
    })
  },

  // 确认数量
  async confirmQuantity() {
    const { product, selectedSpec, quantity, actionType } = this.data
    
    // 计算总价
    let totalPrice = product.price
    if (selectedSpec && product.specs) {
      const spec = product.specs.find(s => s.id === selectedSpec)
      if (spec) {
        totalPrice += spec.price
      }
    }
    totalPrice *= quantity

    if (actionType === 'cart') {
      // 加入购物车
      try {
        // 这里应该调用云函数将商品加入购物车
        // await wx.cloud.callFunction({
        //   name: 'addToCart',
        //   data: {
        //     productId: product.id,
        //     specId: selectedSpec,
        //     quantity: quantity
        //   }
        // })

        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        })
        
        this.hideQuantityModal()
      } catch (error) {
        console.error('加入购物车失败：', error)
        wx.showToast({
          title: '加入失败',
          icon: 'none'
        })
      }
    } else if (actionType === 'buy') {
      // 立即购买，跳转到订单页面
      const orderData = {
        products: [{
          id: product.id,
          name: product.name,
          image: product.images[0],
          price: product.price,
          specId: selectedSpec,
          quantity: quantity,
          totalPrice: totalPrice
        }],
        totalAmount: totalPrice
      }

      wx.navigateTo({
        url: `/pages/order/order?data=${encodeURIComponent(JSON.stringify(orderData))}`
      })

      this.hideQuantityModal()
    }
  }
})
