// address.js
Page({
  data: {
    addressList: [],
    showDeleteModal: false,
    deleteAddressId: null
  },

  onLoad() {
    this.loadAddressList()
  },

  onShow() {
    // 每次显示页面时刷新地址列表
    this.loadAddressList()
  },

  // 加载地址列表
  async loadAddressList() {
    try {
      // 这里应该从云数据库获取地址列表
      // const result = await wx.cloud.callFunction({
      //   name: 'getAddressList',
      //   data: {}
      // })

      // 模拟数据
      const mockAddressList = [
        {
          id: 1,
          name: '张三',
          phone: '138****8888',
          province: '广东省',
          city: '深圳市',
          district: '南山区',
          detail: '科技园南区深南大道10000号腾讯大厦',
          isDefault: true
        },
        {
          id: 2,
          name: '李四',
          phone: '139****9999',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '建国门外大街1号国贸大厦',
          isDefault: false
        },
        {
          id: 3,
          name: '王五',
          phone: '137****7777',
          province: '上海市',
          city: '上海市',
          district: '浦东新区',
          detail: '陆家嘴环路1000号上海中心大厦',
          isDefault: false
        }
      ]

      this.setData({ addressList: mockAddressList })
    } catch (error) {
      console.error('加载地址列表失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 新增地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  },

  // 编辑地址
  editAddress(e) {
    const addressId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${addressId}`
    })
  },

  // 设为默认地址
  async setDefault(e) {
    const addressId = parseInt(e.currentTarget.dataset.id)
    
    try {
      // 这里应该调用云函数设置默认地址
      // await wx.cloud.callFunction({
      //   name: 'setDefaultAddress',
      //   data: { addressId }
      // })

      // 更新本地数据
      const addressList = this.data.addressList.map(item => ({
        ...item,
        isDefault: item.id === addressId
      }))

      this.setData({ addressList })

      wx.showToast({
        title: '设置成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('设置默认地址失败：', error)
      wx.showToast({
        title: '设置失败',
        icon: 'none'
      })
    }
  },

  // 删除地址
  deleteAddress(e) {
    const addressId = parseInt(e.currentTarget.dataset.id)
    this.setData({
      showDeleteModal: true,
      deleteAddressId: addressId
    })
  },

  // 隐藏删除确认弹窗
  hideDeleteModal() {
    this.setData({
      showDeleteModal: false,
      deleteAddressId: null
    })
  },

  // 确认删除
  async confirmDelete() {
    const { deleteAddressId } = this.data
    if (!deleteAddressId) return

    try {
      // 检查是否为默认地址
      const address = this.data.addressList.find(item => item.id === deleteAddressId)
      if (address && address.isDefault && this.data.addressList.length > 1) {
        wx.showModal({
          title: '提示',
          content: '默认地址不能删除，请先设置其他地址为默认地址',
          showCancel: false
        })
        this.hideDeleteModal()
        return
      }

      // 这里应该调用云函数删除地址
      // await wx.cloud.callFunction({
      //   name: 'deleteAddress',
      //   data: { addressId: deleteAddressId }
      // })

      // 更新本地数据
      const addressList = this.data.addressList.filter(item => item.id !== deleteAddressId)
      this.setData({ addressList })

      this.hideDeleteModal()

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('删除地址失败：', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  }
})
