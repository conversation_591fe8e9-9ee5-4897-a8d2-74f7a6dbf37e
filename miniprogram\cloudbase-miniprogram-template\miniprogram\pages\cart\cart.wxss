/* cart.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 购物车列表 */
.cart-list {
  padding: 20rpx;
}

.cart-item {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.item-checkbox {
  margin-right: 15rpx;
  padding-top: 10rpx;
}

.item-content {
  flex: 1;
  display: flex;
  margin-right: 15rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 1.4;
}

.item-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: block;
}

.item-price {
  margin-bottom: 10rpx;
}

.price-symbol {
  font-size: 22rpx;
  color: #FF6B9D;
}

.price-value {
  font-size: 26rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.item-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: 120rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: #f5f5f5;
  border: none;
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn[disabled] {
  color: #ccc;
}

.quantity-btn.decrease {
  border-right: 1rpx solid #ddd;
}

.quantity-btn.increase {
  border-left: 1rpx solid #ddd;
}

.quantity-value {
  width: 60rpx;
  height: 50rpx;
  text-align: center;
  font-size: 24rpx;
  line-height: 50rpx;
  background: white;
}

.delete-btn {
  background: none;
  border: none;
  color: #999;
  font-size: 22rpx;
  padding: 0;
  margin-top: 10rpx;
}

/* 空购物车状态 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.5;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  display: block;
}

.go-shopping-btn {
  background-color: #FF6B9D;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 40rpx;
  font-size: 26rpx;
}

/* 推荐商品 */
.recommend-section {
  background: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx 20rpx 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  vertical-align: top;
}

.recommend-item:last-child {
  margin-right: 0;
}

.recommend-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.recommend-name {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 1.3;
  height: 62rpx;
}

.recommend-price {
  margin-bottom: 10rpx;
}

.recommend-price .price-symbol {
  font-size: 20rpx;
}

.recommend-price .price-value {
  font-size: 24rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  z-index: 100;
}

.select-all {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.select-all-text {
  font-size: 26rpx;
  color: #333;
  margin-left: 10rpx;
}

.total-info {
  flex: 1;
  text-align: right;
  margin-right: 20rpx;
}

.total-text {
  font-size: 24rpx;
  color: #666;
}

.total-symbol {
  font-size: 24rpx;
  color: #FF6B9D;
}

.total-amount {
  font-size: 32rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.checkout-btn {
  background-color: #ccc;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  font-weight: bold;
}

.checkout-btn.active {
  background-color: #FF6B9D;
}

.checkout-btn[disabled] {
  background-color: #ccc;
}

/* 删除确认弹窗 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-modal {
  width: 500rpx;
  background: white;
  border-radius: 12rpx;
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.modal-content {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
}

.modal-cancel,
.modal-confirm {
  flex: 1;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  font-size: 26rpx;
}

.modal-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-confirm {
  background-color: #FF6B9D;
  color: white;
}
