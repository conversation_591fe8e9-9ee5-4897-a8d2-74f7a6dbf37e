# 手办电商小程序

一个基于微信小程序和腾讯云开发的完整手办电商平台，提供商品展示、购物车、订单管理、用户中心、管理后台等完整功能。

[![Powered by CloudBase](https://7463-tcb-advanced-a656fc-1257967285.tcb.qcloud.la/mcp/powered-by-cloudbase-badge.svg)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit)

> 本项目基于 [**CloudBase AI ToolKit**](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit) 开发，通过AI提示词和 MCP 协议+云开发，让开发更智能、更高效，支持AI生成全栈代码、一键部署至腾讯云开发（免服务器）、智能日志修复。

## 项目特色

- 🎯 **专业手办电商** - 专门为手办爱好者打造的购物平台
- 🎨 **现代化UI设计** - 清新粉色主题，符合年轻用户审美
- 📱 **完整小程序体验** - 原生小程序开发，流畅用户体验
- ☁️ **云开发架构** - 基于腾讯云开发，无需服务器运维
- 🛒 **完整电商功能** - 商品管理、购物车、订单、支付等全流程
- 👨‍💼 **管理后台** - 完整的商品和订单管理系统

## 项目架构

### 前端架构
```
miniprogram/
├── pages/                 # 页面目录
│   ├── index/             # 首页 - 轮播图、商品推荐
│   ├── category/          # 分类页 - 商品分类浏览
│   ├── product-list/      # 商品列表 - 筛选、排序
│   ├── product-detail/    # 商品详情 - 详情展示、购买
│   ├── cart/              # 购物车 - 商品管理、结算
│   ├── order/             # 确认订单 - 地址、支付方式
│   ├── user/              # 用户中心 - 个人信息、订单
│   ├── address/           # 地址管理 - 收货地址CRUD
│   └── admin/             # 管理后台 - 数据统计、管理入口
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
└── app.wxss              # 全局样式文件
```

### 云开发资源

#### 数据库集合设计
- **products** - 商品信息
  - id, name, price, images, category, stock, description, specs
- **orders** - 订单信息
  - id, userId, products, address, status, amount, createTime
- **users** - 用户信息
  - openId, nickName, avatarUrl, phone, addresses
- **categories** - 商品分类
  - id, name, icon, sort
- **addresses** - 收货地址
  - id, userId, name, phone, province, city, district, detail, isDefault

#### 云函数列表
- **getProducts** - 获取商品列表（分页、筛选、排序）
- **getProductDetail** - 获取商品详情
- **createOrder** - 创建订单
- **updateOrderStatus** - 更新订单状态
- **getUserOrders** - 获取用户订单列表
- **manageAddress** - 地址管理（增删改查）
- **adminDashboard** - 管理后台数据统计
- **userLogin** - 用户登录注册

#### 云存储
- **product-images/** - 商品图片存储
- **user-avatars/** - 用户头像存储
- **category-icons/** - 分类图标存储

## 功能特性

### 用户端功能
- ✅ **首页展示** - 轮播图、热门商品、分类导航
- ✅ **商品浏览** - 分类筛选、搜索、排序
- ✅ **商品详情** - 图片轮播、规格选择、评价展示
- ✅ **购物车** - 商品管理、数量调整、批量操作
- ✅ **订单流程** - 地址选择、支付方式、订单确认
- ✅ **用户中心** - 个人信息、订单查询、地址管理
- ✅ **地址管理** - 新增、编辑、删除、设为默认

### 管理端功能
- ✅ **数据概览** - 订单统计、营收分析、用户数据
- ✅ **待处理事项** - 待付款、待发货、库存预警
- ✅ **订单管理** - 订单查询、状态更新、发货处理
- ✅ **商品管理** - 商品CRUD、库存管理、分类管理
- ✅ **用户管理** - 用户信息、订单历史、权限管理

## 技术栈

- **前端框架**: 微信小程序原生开发
- **UI设计**: WXSS + 响应式布局
- **后端服务**: 腾讯云开发 CloudBase
- **数据库**: 云数据库 MongoDB
- **文件存储**: 云存储 COS
- **支付集成**: 微信支付
- **用户认证**: 微信登录

## 快速开始

### 环境准备
1. 安装微信开发者工具
2. 注册腾讯云开发环境
3. 获取小程序AppID

### 本地开发
1. 克隆项目到本地
2. 用微信开发者工具打开项目
3. 配置云开发环境ID
4. 部署云函数和数据库
5. 启动本地调试

### 部署上线
1. 云函数部署到生产环境
2. 数据库权限配置
3. 小程序代码上传审核
4. 配置支付参数
5. 发布上线

## 项目亮点

### UI/UX设计
- 🎨 **粉色主题** - 温馨可爱的视觉风格
- 📱 **响应式设计** - 适配各种屏幕尺寸
- 🎯 **用户体验** - 流畅的交互动画和反馈
- 🖼️ **高质量素材** - 使用Unsplash高质量图片

### 技术实现
- ⚡ **性能优化** - 图片懒加载、分页加载
- 🔒 **数据安全** - 云函数权限控制、数据校验
- 📊 **状态管理** - 完善的数据流管理
- 🛠️ **错误处理** - 完整的异常处理机制

### 业务逻辑
- 🛒 **购物流程** - 完整的电商购物体验
- 💰 **支付集成** - 微信支付无缝集成
- 📦 **订单管理** - 完整的订单生命周期
- 👥 **用户体系** - 完善的用户权限管理

## 开发计划

### 已完成功能 ✅
- [x] 项目架构搭建
- [x] 首页和商品页面
- [x] 购物车和订单系统
- [x] 用户中心和地址管理
- [x] 管理后台基础功能

### 待开发功能 🚧
- [ ] 云函数和数据库实现
- [ ] 支付功能集成
- [ ] 商品评价系统
- [ ] 优惠券系统
- [ ] 数据分析功能

### 未来规划 🔮
- [ ] 直播带货功能
- [ ] 社区互动功能
- [ ] AI推荐算法
- [ ] 多端同步（H5、APP）

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

## 许可证

MIT License

---

**手办电商小程序** - 让每个手办爱好者都能找到心仪的收藏品 ❤️