<!--cart.wxml-->
<view class="container">
  <!-- 购物车商品列表 -->
  <view class="cart-list" wx:if="{{cartItems.length > 0}}">
    <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
      <!-- 选择框 -->
      <view class="item-checkbox">
        <checkbox 
          value="{{item.id}}" 
          checked="{{item.selected}}" 
          bindtap="toggleSelect" 
          data-id="{{item.id}}"
        ></checkbox>
      </view>
      
      <!-- 商品信息 -->
      <view class="item-content" bindtap="goToDetail" data-id="{{item.productId}}">
        <image src="{{item.image}}" class="item-image" mode="aspectFill"></image>
        <view class="item-info">
          <text class="item-name">{{item.name}}</text>
          <text class="item-spec" wx:if="{{item.spec}}">规格：{{item.spec}}</text>
          <view class="item-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
          </view>
        </view>
      </view>
      
      <!-- 数量控制和删除 -->
      <view class="item-actions">
        <view class="quantity-control">
          <button 
            class="quantity-btn decrease" 
            bindtap="decreaseQuantity" 
            data-id="{{item.id}}"
            disabled="{{item.quantity <= 1}}"
          >-</button>
          <text class="quantity-value">{{item.quantity}}</text>
          <button 
            class="quantity-btn increase" 
            bindtap="increaseQuantity" 
            data-id="{{item.id}}"
          >+</button>
        </view>
        <button class="delete-btn" bindtap="deleteItem" data-id="{{item.id}}">删除</button>
      </view>
    </view>
  </view>

  <!-- 空购物车状态 -->
  <view class="empty-cart" wx:else>
    <image src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=200&h=200&fit=crop" class="empty-image"></image>
    <text class="empty-text">购物车空空如也</text>
    <button class="go-shopping-btn" bindtap="goShopping">去逛逛</button>
  </view>

  <!-- 推荐商品 -->
  <view class="recommend-section" wx:if="{{recommendProducts.length > 0}}">
    <view class="section-title">为你推荐</view>
    <scroll-view class="recommend-scroll" scroll-x="true">
      <view class="recommend-item" wx:for="{{recommendProducts}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <image src="{{item.image}}" class="recommend-image" mode="aspectFill"></image>
        <text class="recommend-name">{{item.name}}</text>
        <view class="recommend-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{item.price}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar" wx:if="{{cartItems.length > 0}}">
    <view class="select-all">
      <checkbox 
        checked="{{selectAll}}" 
        bindtap="toggleSelectAll"
      ></checkbox>
      <text class="select-all-text">全选</text>
    </view>
    
    <view class="total-info">
      <text class="total-text">合计：</text>
      <text class="total-symbol">¥</text>
      <text class="total-amount">{{totalAmount}}</text>
    </view>
    
    <button 
      class="checkout-btn {{selectedItems.length > 0 ? 'active' : ''}}" 
      bindtap="checkout"
      disabled="{{selectedItems.length === 0}}"
    >
      结算({{selectedItems.length}})
    </button>
  </view>

  <!-- 删除确认弹窗 -->
  <view class="modal-mask" wx:if="{{showDeleteModal}}" bindtap="hideDeleteModal">
    <view class="delete-modal" catchtap="">
      <view class="modal-title">确认删除</view>
      <view class="modal-content">确定要删除这个商品吗？</view>
      <view class="modal-actions">
        <button class="modal-cancel" bindtap="hideDeleteModal">取消</button>
        <button class="modal-confirm" bindtap="confirmDelete">确定</button>
      </view>
    </view>
  </view>
</view>
