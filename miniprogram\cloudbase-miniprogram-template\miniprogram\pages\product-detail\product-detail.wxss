/* product-detail.wxss */
.container {
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 商品图片轮播 */
.product-swiper {
  height: 600rpx;
  background: white;
}

.product-image {
  width: 100%;
  height: 100%;
}

/* 商品基本信息 */
.product-info {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-header {
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 15rpx;
  display: block;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.tag {
  padding: 6rpx 12rpx;
  background-color: #FF6B9D;
  color: white;
  font-size: 22rpx;
  border-radius: 12rpx;
}

.price-section {
  margin-bottom: 25rpx;
}

.current-price {
  margin-bottom: 10rpx;
}

.price-symbol {
  font-size: 28rpx;
  color: #FF6B9D;
}

.price-value {
  font-size: 42rpx;
  color: #FF6B9D;
  font-weight: bold;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.stat-value {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 规格选择 */
.spec-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.spec-option {
  padding: 15rpx 25rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
}

.spec-option.active {
  border-color: #FF6B9D;
  background-color: #fff0f5;
  color: #FF6B9D;
}

.spec-price {
  font-size: 22rpx;
  margin-top: 5rpx;
}

/* 商品详情 */
.detail-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.detail-content {
  line-height: 1.6;
}

.detail-text {
  font-size: 26rpx;
  color: #666;
  white-space: pre-line;
  margin-bottom: 30rpx;
  display: block;
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-image {
  width: 100%;
  border-radius: 8rpx;
}

/* 用户评价 */
.review-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
}

.view-all {
  font-size: 24rpx;
  color: #FF6B9D;
}

.review-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.review-item {
  border-bottom: 1rpx solid #f5f5f5;
  padding-bottom: 25rpx;
}

.review-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 5rpx;
}

.rating {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.stars {
  font-size: 22rpx;
  color: #ff9500;
}

.review-date {
  font-size: 22rpx;
  color: #999;
}

.review-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
  display: block;
}

.review-images {
  display: flex;
  gap: 10rpx;
}

.review-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  z-index: 100;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.btn-cart,
.btn-buy {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.btn-cart {
  background-color: #ffa500;
  color: white;
}

.btn-buy {
  background-color: #FF6B9D;
  color: white;
}

.btn-text {
  color: inherit;
}

/* 数量选择弹窗 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.quantity-modal {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 0;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-section {
  padding: 30rpx;
}

.quantity-info {
  display: flex;
  margin-bottom: 30rpx;
}

.quantity-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.quantity-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.quantity-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.quantity-price {
  font-size: 28rpx;
  color: #FF6B9D;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.quantity-stock {
  font-size: 22rpx;
  color: #999;
  display: block;
}

.quantity-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-label {
  font-size: 26rpx;
  color: #333;
}

.quantity-input {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border: none;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn[disabled] {
  color: #ccc;
}

.quantity-value {
  width: 100rpx;
  height: 60rpx;
  text-align: center;
  border: none;
  font-size: 26rpx;
}

.quantity-actions {
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.quantity-confirm {
  width: 100%;
  height: 80rpx;
  background-color: #FF6B9D;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}
