/* index.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 轮播图样式 */
.banner-swiper {
  height: 400rpx;
  margin-bottom: 20rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类导航样式 */
.category-nav {
  display: flex;
  justify-content: space-around;
  background: white;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 10rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #333;
}

/* 商品区块样式 */
.section {
  background: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #FF6B9D;
}

/* 横向滚动商品列表 */
.product-scroll {
  white-space: nowrap;
}

.product-item {
  display: inline-block;
  width: 240rpx;
  margin-right: 20rpx;
  vertical-align: top;
}

.product-item:last-child {
  margin-right: 0;
}

.product-image {
  width: 100%;
  height: 240rpx;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.product-info {
  text-align: center;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  color: #FF6B9D;
  font-weight: bold;
}

.price-symbol {
  font-size: 24rpx;
}

.price-value {
  font-size: 28rpx;
}

/* 精选商品网格布局 */
.featured-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.featured-item {
  display: flex;
  background: #f9f9f9;
  border-radius: 12rpx;
  overflow: hidden;
}

.featured-image {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.featured-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.featured-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.featured-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.featured-price {
  color: #FF6B9D;
  font-weight: bold;
}
