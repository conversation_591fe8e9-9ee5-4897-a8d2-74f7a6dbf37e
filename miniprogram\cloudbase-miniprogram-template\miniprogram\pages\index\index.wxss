.container {
  min-height: 100vh;
  padding: 40rpx 60rpx;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.subtitle {
  display: block;
  font-size: 32rpx;
  color: rgba(255,255,255,0.8);
  font-weight: 300;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 60rpx;
}

.info-card {
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  backdrop-filter: blur(10rpx);
}

.label {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.value {
  display: block;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  word-break: break-all;
  font-family: 'Monaco', 'Menlo', monospace;
}

.features {
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  backdrop-filter: blur(10rpx);
}

.features-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.feature-item {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  line-height: 1.6;
}

.footer {
  text-align: center;
  margin-top: 60rpx;
  padding-top: 40rpx;
}

/* badge样式已移到组件中 */
