<!--category.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-bar">
    <input class="search-input" placeholder="搜索手办..." bindinput="onSearchInput" value="{{searchKeyword}}" />
    <button class="search-btn" bindtap="onSearch">搜索</button>
  </view>

  <view class="content">
    <!-- 左侧分类列表 -->
    <scroll-view class="category-list" scroll-y="true">
      <view 
        class="category-item {{currentCategory === item.id ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id"
        bindtap="onCategoryTap" 
        data-id="{{item.id}}"
      >
        <text class="category-name">{{item.name}}</text>
      </view>
    </scroll-view>

    <!-- 右侧商品列表 -->
    <scroll-view class="product-list" scroll-y="true">
      <view class="product-grid">
        <view 
          class="product-item" 
          wx:for="{{products}}" 
          wx:key="id"
          bindtap="onProductTap" 
          data-id="{{item.id}}"
        >
          <image src="{{item.image}}" class="product-image" mode="aspectFill"></image>
          <view class="product-info">
            <text class="product-name">{{item.name}}</text>
            <view class="product-price">
              <text class="price-symbol">¥</text>
              <text class="price-value">{{item.price}}</text>
            </view>
            <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
              <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore}}">
        <button class="load-more-btn" bindtap="loadMore" loading="{{loading}}">
          {{loading ? '加载中...' : '加载更多'}}
        </button>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
        <text>没有更多商品了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{products.length === 0 && !loading}}">
        <text class="empty-text">暂无商品</text>
      </view>
    </scroll-view>
  </view>
</view>
