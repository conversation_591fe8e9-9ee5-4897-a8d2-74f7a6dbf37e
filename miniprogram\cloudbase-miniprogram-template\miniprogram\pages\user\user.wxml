<!--user.wxml-->
<view class="container">
  <!-- 用户信息头部 -->
  <view class="user-header">
    <view class="user-info" wx:if="{{userInfo}}">
      <image src="{{userInfo.avatarUrl}}" class="user-avatar" bindtap="updateAvatar"></image>
      <view class="user-details">
        <text class="username">{{userInfo.nickName}}</text>
        <text class="user-level">普通会员</text>
      </view>
    </view>
    <view class="login-section" wx:else>
      <image src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face" class="default-avatar"></image>
      <view class="login-info">
        <text class="login-text">点击登录</text>
        <button class="login-btn" bindtap="login">立即登录</button>
      </view>
    </view>
    <view class="user-stats" wx:if="{{userInfo}}">
      <view class="stat-item" bindtap="goToOrders">
        <text class="stat-number">{{orderStats.total || 0}}</text>
        <text class="stat-label">全部订单</text>
      </view>
      <view class="stat-item" bindtap="goToFavorites">
        <text class="stat-number">{{favoriteCount || 0}}</text>
        <text class="stat-label">收藏商品</text>
      </view>
      <view class="stat-item" bindtap="goToCoupons">
        <text class="stat-number">{{couponCount || 0}}</text>
        <text class="stat-label">优惠券</text>
      </view>
    </view>
  </view>

  <!-- 订单状态 -->
  <view class="order-section" wx:if="{{userInfo}}">
    <view class="section-header">
      <text class="section-title">我的订单</text>
      <text class="view-all" bindtap="goToOrders">查看全部 ></text>
    </view>
    <view class="order-status">
      <view class="status-item" bindtap="goToOrders" data-status="pending">
        <image src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=50&h=50&fit=crop" class="status-icon"></image>
        <text class="status-text">待付款</text>
        <text class="status-count" wx:if="{{orderStats.pending > 0}}">{{orderStats.pending}}</text>
      </view>
      <view class="status-item" bindtap="goToOrders" data-status="paid">
        <image src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=50&h=50&fit=crop" class="status-icon"></image>
        <text class="status-text">待发货</text>
        <text class="status-count" wx:if="{{orderStats.paid > 0}}">{{orderStats.paid}}</text>
      </view>
      <view class="status-item" bindtap="goToOrders" data-status="shipped">
        <image src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=50&h=50&fit=crop" class="status-icon"></image>
        <text class="status-text">待收货</text>
        <text class="status-count" wx:if="{{orderStats.shipped > 0}}">{{orderStats.shipped}}</text>
      </view>
      <view class="status-item" bindtap="goToOrders" data-status="completed">
        <image src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=50&h=50&fit=crop" class="status-icon"></image>
        <text class="status-text">已完成</text>
        <text class="status-count" wx:if="{{orderStats.completed > 0}}">{{orderStats.completed}}</text>
      </view>
      <view class="status-item" bindtap="goToOrders" data-status="refund">
        <image src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=50&h=50&fit=crop" class="status-icon"></image>
        <text class="status-text">退款/售后</text>
        <text class="status-count" wx:if="{{orderStats.refund > 0}}">{{orderStats.refund}}</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="goToAddresses">
        <image src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=50&h=50&fit=crop" class="menu-icon"></image>
        <text class="menu-text">收货地址</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="goToCoupons">
        <image src="https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=50&h=50&fit=crop" class="menu-icon"></image>
        <text class="menu-text">优惠券</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="goToFavorites">
        <image src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=50&h=50&fit=crop" class="menu-icon"></image>
        <text class="menu-text">我的收藏</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="goToCustomerService">
        <image src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=50&h=50&fit=crop" class="menu-icon"></image>
        <text class="menu-text">客服中心</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="goToHelp">
        <image src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=50&h=50&fit=crop" class="menu-icon"></image>
        <text class="menu-text">帮助中心</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="goToSettings">
        <image src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=50&h=50&fit=crop" class="menu-icon"></image>
        <text class="menu-text">设置</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 管理员入口 -->
    <view class="menu-group" wx:if="{{isAdmin}}">
      <view class="menu-item admin-item" bindtap="goToAdmin">
        <image src="https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=50&h=50&fit=crop" class="menu-icon"></image>
        <text class="menu-text">管理后台</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{userInfo}}">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
