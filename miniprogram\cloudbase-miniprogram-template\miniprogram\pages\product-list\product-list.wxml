<!--product-list.wxml-->
<view class="container">
  <!-- 筛选和排序栏 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="showSortModal">
      <text class="filter-text">{{sortText}}</text>
      <text class="filter-arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="showFilterModal">
      <text class="filter-text">筛选</text>
      <text class="filter-arrow">▼</text>
    </view>
    <view class="filter-item" bindtap="toggleLayout">
      <text class="filter-text">{{layoutType === 'grid' ? '列表' : '网格'}}</text>
    </view>
  </view>

  <!-- 商品列表 -->
  <scroll-view class="product-list" scroll-y="true" bindscrolltolower="loadMore">
    <view class="product-container {{layoutType}}">
      <view 
        class="product-item" 
        wx:for="{{products}}" 
        wx:key="id"
        bindtap="onProductTap" 
        data-id="{{item.id}}"
      >
        <image src="{{item.image}}" class="product-image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-price">
            <text class="price-symbol">¥</text>
            <text class="price-value">{{item.price}}</text>
            <text class="original-price" wx:if="{{item.originalPrice}}">¥{{item.originalPrice}}</text>
          </view>
          <view class="product-tags" wx:if="{{item.tags && item.tags.length > 0}}">
            <text class="tag" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
          <view class="product-stats" wx:if="{{layoutType === 'list'}}">
            <text class="sales">已售{{item.sales || 0}}件</text>
            <text class="rating">★{{item.rating || 5.0}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="load-status">
      <view wx:if="{{loading}}" class="loading">
        <text>加载中...</text>
      </view>
      <view wx:elif="{{!hasMore && products.length > 0}}" class="no-more">
        <text>没有更多商品了</text>
      </view>
      <view wx:elif="{{products.length === 0}}" class="empty">
        <text>暂无商品</text>
      </view>
    </view>
  </scroll-view>

  <!-- 排序弹窗 -->
  <view class="modal-mask" wx:if="{{showSort}}" bindtap="hideSortModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">排序方式</text>
        <text class="modal-close" bindtap="hideSortModal">×</text>
      </view>
      <view class="sort-options">
        <view 
          class="sort-option {{currentSort === item.value ? 'active' : ''}}" 
          wx:for="{{sortOptions}}" 
          wx:key="value"
          bindtap="selectSort" 
          data-value="{{item.value}}"
        >
          <text>{{item.label}}</text>
          <text class="check" wx:if="{{currentSort === item.value}}">✓</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 筛选弹窗 -->
  <view class="modal-mask" wx:if="{{showFilter}}" bindtap="hideFilterModal">
    <view class="modal-content filter-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">商品筛选</text>
        <text class="modal-close" bindtap="hideFilterModal">×</text>
      </view>
      <view class="filter-content">
        <!-- 价格区间 -->
        <view class="filter-section">
          <text class="filter-section-title">价格区间</text>
          <view class="price-range">
            <input class="price-input" placeholder="最低价" type="number" bindinput="onMinPriceInput" value="{{minPrice}}" />
            <text class="price-separator">-</text>
            <input class="price-input" placeholder="最高价" type="number" bindinput="onMaxPriceInput" value="{{maxPrice}}" />
          </view>
        </view>
        
        <!-- 商品标签 -->
        <view class="filter-section">
          <text class="filter-section-title">商品标签</text>
          <view class="tag-options">
            <view
              class="tag-option {{selectedTags.includes(item) ? 'active' : ''}}"
              wx:for="{{tagOptions}}"
              wx:key="*this"
              bindtap="toggleTag"
              data-tag="{{item}}"
            >
              <text>{{item}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="filter-actions">
        <button class="filter-reset" bindtap="resetFilter">重置</button>
        <button class="filter-confirm" bindtap="applyFilter">确定</button>
      </view>
    </view>
  </view>
</view>
