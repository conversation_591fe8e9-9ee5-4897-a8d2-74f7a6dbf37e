/* admin.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding: 20rpx;
}

/* 统计概览 */
.stats-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: white;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
}

.stats-date {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 20rpx;
  background-color: rgba(255,255,255,0.1);
  border-radius: 8rpx;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.8;
  display: block;
}

/* 通用标题 */
.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.view-all {
  font-size: 24rpx;
  color: #667eea;
}

/* 快捷操作 */
.quick-actions {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 10rpx;
  border-radius: 8rpx;
  background-color: #f8f9ff;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 22rpx;
  color: #333;
  text-align: center;
}

/* 待处理事项 */
.pending-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.pending-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9ff;
  border-radius: 8rpx;
  border-left: 4rpx solid #667eea;
}

.pending-info {
  flex: 1;
}

.pending-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
  display: block;
}

.pending-desc {
  font-size: 22rpx;
  color: #666;
  display: block;
}

.pending-count {
  background-color: #667eea;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  min-width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15rpx;
}

/* 最近订单 */
.recent-orders {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9ff;
  border-radius: 8rpx;
  position: relative;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 5rpx;
  display: block;
}

.order-time {
  font-size: 20rpx;
  color: #999;
  display: block;
}

.order-details {
  flex: 1;
  text-align: center;
}

.order-customer {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 5rpx;
  display: block;
}

.order-amount {
  font-size: 26rpx;
  font-weight: bold;
  color: #667eea;
  display: block;
}

.order-status {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: bold;
}

.order-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.order-status.paid {
  background-color: #d4edda;
  color: #155724;
}

.order-status.shipped {
  background-color: #cce5ff;
  color: #004085;
}

.order-status.completed {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* 系统设置 */
.system-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.setting-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9ff;
  border-radius: 8rpx;
}

.setting-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.setting-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.setting-arrow {
  font-size: 24rpx;
  color: #999;
}
