// cart.js
Page({
  data: {
    cartItems: [],
    recommendProducts: [],
    selectAll: false,
    selectedItems: [],
    totalAmount: 0,
    showDeleteModal: false,
    deleteItemId: null
  },

  onLoad() {
    this.loadCartItems()
    this.loadRecommendProducts()
  },

  onShow() {
    // 每次显示页面时刷新购物车数据
    this.loadCartItems()
  },

  // 加载购物车商品
  async loadCartItems() {
    try {
      // 模拟数据，实际应该从云数据库获取
      const mockCartItems = [
        {
          id: 1,
          productId: 1,
          name: '初音未来 雪初音版 1/8 完成品手办',
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          price: 299.00,
          quantity: 1,
          spec: '标准版',
          selected: false,
          stock: 99
        },
        {
          id: 2,
          productId: 2,
          name: '海贼王 路飞 四档 战斗版手办',
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop',
          price: 399.00,
          quantity: 2,
          spec: '豪华版',
          selected: false,
          stock: 50
        },
        {
          id: 3,
          productId: 3,
          name: '鬼灭之刃 炭治郎 DX版手办',
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          price: 259.00,
          quantity: 1,
          spec: '限定版',
          selected: false,
          stock: 20
        }
      ]

      this.setData({ cartItems: mockCartItems })
      this.updateSelectAll()
      this.calculateTotal()
    } catch (error) {
      console.error('加载购物车失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  },

  // 加载推荐商品
  async loadRecommendProducts() {
    try {
      const mockRecommendProducts = [
        {
          id: 4,
          name: '原神 甘雨',
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop',
          price: 459.00
        },
        {
          id: 5,
          name: '明日方舟 德克萨斯',
          image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop',
          price: 329.00
        },
        {
          id: 6,
          name: '高达 RX-78-2',
          image: 'https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=300&h=300&fit=crop',
          price: 599.00
        }
      ]

      this.setData({ recommendProducts: mockRecommendProducts })
    } catch (error) {
      console.error('加载推荐商品失败：', error)
    }
  },

  // 切换单个商品选择状态
  toggleSelect(e) {
    const itemId = parseInt(e.currentTarget.dataset.id)
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === itemId) {
        return { ...item, selected: !item.selected }
      }
      return item
    })

    this.setData({ cartItems })
    this.updateSelectAll()
    this.calculateTotal()
  },

  // 切换全选状态
  toggleSelectAll() {
    const selectAll = !this.data.selectAll
    const cartItems = this.data.cartItems.map(item => ({
      ...item,
      selected: selectAll
    }))

    this.setData({ 
      cartItems,
      selectAll 
    })
    this.calculateTotal()
  },

  // 更新全选状态
  updateSelectAll() {
    const { cartItems } = this.data
    const selectAll = cartItems.length > 0 && cartItems.every(item => item.selected)
    this.setData({ selectAll })
  },

  // 计算总价
  calculateTotal() {
    const selectedItems = this.data.cartItems.filter(item => item.selected)
    const totalAmount = selectedItems.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)

    this.setData({ 
      selectedItems,
      totalAmount: totalAmount.toFixed(2)
    })
  },

  // 增加数量
  async increaseQuantity(e) {
    const itemId = parseInt(e.currentTarget.dataset.id)
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === itemId && item.quantity < item.stock) {
        return { ...item, quantity: item.quantity + 1 }
      }
      return item
    })

    this.setData({ cartItems })
    this.calculateTotal()

    // 这里应该调用云函数更新购物车
    // await this.updateCartItem(itemId, newQuantity)
  },

  // 减少数量
  async decreaseQuantity(e) {
    const itemId = parseInt(e.currentTarget.dataset.id)
    const cartItems = this.data.cartItems.map(item => {
      if (item.id === itemId && item.quantity > 1) {
        return { ...item, quantity: item.quantity - 1 }
      }
      return item
    })

    this.setData({ cartItems })
    this.calculateTotal()

    // 这里应该调用云函数更新购物车
    // await this.updateCartItem(itemId, newQuantity)
  },

  // 删除商品
  deleteItem(e) {
    const itemId = parseInt(e.currentTarget.dataset.id)
    this.setData({
      showDeleteModal: true,
      deleteItemId: itemId
    })
  },

  // 显示删除确认弹窗
  showDeleteModal() {
    this.setData({ showDeleteModal: true })
  },

  // 隐藏删除确认弹窗
  hideDeleteModal() {
    this.setData({ 
      showDeleteModal: false,
      deleteItemId: null
    })
  },

  // 确认删除
  async confirmDelete() {
    const { deleteItemId } = this.data
    if (!deleteItemId) return

    try {
      const cartItems = this.data.cartItems.filter(item => item.id !== deleteItemId)
      this.setData({ cartItems })
      this.updateSelectAll()
      this.calculateTotal()
      this.hideDeleteModal()

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })

      // 这里应该调用云函数删除购物车商品
      // await wx.cloud.callFunction({
      //   name: 'deleteCartItem',
      //   data: { itemId: deleteItemId }
      // })
    } catch (error) {
      console.error('删除商品失败：', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  },

  // 跳转到商品详情
  goToDetail(e) {
    const productId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    })
  },

  // 去购物
  goShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 结算
  checkout() {
    const { selectedItems } = this.data
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      })
      return
    }

    // 构造订单数据
    const orderData = {
      products: selectedItems.map(item => ({
        id: item.productId,
        cartItemId: item.id,
        name: item.name,
        image: item.image,
        price: item.price,
        quantity: item.quantity,
        spec: item.spec,
        totalPrice: item.price * item.quantity
      })),
      totalAmount: parseFloat(this.data.totalAmount)
    }

    // 跳转到订单页面
    wx.navigateTo({
      url: `/pages/order/order?data=${encodeURIComponent(JSON.stringify(orderData))}`
    })
  }
})
